<template>
  <div
    class="ms-bc-connect-card"
    :class="tokenStatus === 'success' ? 'connected' : ''"
  >
    <div class="header-left" v-if="tokenStatus !== 'success'">
      <h3 class="subheader--light">Microsoft Business Central Connection</h3>
      <div class="status-chips">
        <v-chip :color="msStatusInfo.color" small class="mr-2">
          {{ msStatusInfo.text }}
        </v-chip>
        <v-chip :color="tokenStatusInfo.color" small>{{
          tokenStatusInfo.text
        }}</v-chip>
      </div>
    </div>
    <div v-if="!props.isEdit" class="info-grid">
      <div class="info-item">
        <span class="info-label">Tenant ID</span>
        <span class="info-value">{{ bcTokenId || '—' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Environment</span>
        <span class="info-value">{{ bcEnvironment || '—' }}</span>
      </div>
      <div class="info-item" v-if="tokenStatus !== 'success'">
        <span class="info-label">Last Used URL</span>
        <span class="info-value last-url">{{ lastUsedBcUrl || '—' }}</span>
      </div>
      <div class="connected-info-item" v-else>
        <v-flex class="connected-info-label"
          >Connected to Microsoft Business Central
          <i class="fad fa-check-double pt-1 pl-2"></i>
        </v-flex>
      </div>
    </div>
    <div
      v-if="!(msStatus === 'received' && tokenStatus === 'success')"
      class="form-row"
    >
      <v-text-field
        v-if="!props.isEdit"
        v-model="inputBcUrl"
        label="Business Central URL"
        hint="Business Central URL"
        :placeholder="lastUsedBcUrl"
        dense
        hide-details
      >
        <template #append>
          <v-btn
            icon
            @click="pasteFromClipboard"
            :disabled="loading"
            class="paste-btn"
          >
            <v-icon size="20">content_paste</v-icon>
          </v-btn>
        </template>
      </v-text-field>
      <v-btn
        :loading="loading"
        color="primary"
        @click="startAuthFlow"
        :disabled="loading"
        class="connect-btn"
        block
      >
        <v-icon left class="pr-2">key</v-icon>
        Connect
      </v-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CreateExportUserResponse } from '@/interface-models/InvoicingIntegration/ExportUser';
import GetTokensFromOauth2AuthCodeRequest from '@/interface-models/InvoicingIntegration/GetTokensFromOauth2AuthCodeRequest';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import {
  computed,
  defineEmits,
  defineProps,
  onBeforeUnmount,
  onMounted,
  Ref,
  ref,
} from 'vue';

const props = withDefaults(
  defineProps<{
    redirectUri: string;
    tenantId: string;
    isEdit?: boolean;
  }>(),
  {
    isEdit: false,
  },
);

const emit = defineEmits<{
  (e: 'connectionSuccessful', payload: CreateExportUserResponse): void;
}>();

const integrationStore = useIntegrationStore();

const loading: Ref<boolean> = ref(false);
const popupInterval: Ref<number | null> = ref(null);
const popup: Ref<Window | null> = ref(null);

const msStatus = ref<'not connected' | 'opened' | 'received' | 'failed'>(
  'not connected',
);
const tokenStatus = ref<'no token' | 'exchanging' | 'success' | 'failed'>(
  'no token',
);

const inputBcUrl: Ref<string> = ref('');

const lastUsedBcUrl: Ref<string> = ref(
  'https://businesscentral.dynamics.com/c915ca25-3b9e-4765-9768-212473b32d30/Sandbox',
);

const exportTypes = computed(() => {
  return integrationStore.exportTypes;
});

const effectiveBcUrl = computed(
  () => inputBcUrl.value?.trim() || lastUsedBcUrl.value,
);

const bcTokenId = computed(() => {
  try {
    const url = new URL(effectiveBcUrl.value);
    const parts = url.pathname.split('/');
    return parts[1] || '';
  } catch {
    return '';
  }
});

const bcEnvironment = computed(() => {
  try {
    const url = new URL(effectiveBcUrl.value);
    const parts = url.pathname.split('/');
    return parts[2] || '';
  } catch {
    return '';
  }
});

// Status mappings for chips
const statusMappings = {
  ms: {
    opened: { text: 'Auth Window Opened', color: 'info' },
    received: { text: 'Auth Code Received', color: 'success' },
    failed: { text: 'Auth Failed', color: 'error' },
    'not connected': { text: 'Not Connected', color: 'error' },
  },
  token: {
    exchanging: { text: 'Exchanging Token...', color: 'info' },
    success: { text: 'Token Success', color: 'success' },
    failed: { text: 'Token Failed', color: 'error' },
    'no token': { text: 'No Token', color: 'none' },
  },
};

// Consolidated computed properties for status chips
const msStatusInfo = computed(() => {
  return (
    statusMappings.ms[msStatus.value] || statusMappings.ms['not connected']
  );
});

const tokenStatusInfo = computed(() => {
  return (
    statusMappings.token[tokenStatus.value] || statusMappings.token['no token']
  );
});

/**
 * Reads text from the system clipboard and sets it as the Business Central URL input value.
 * Handles clipboard access errors gracefully by logging them to console.
 */
async function pasteFromClipboard() {
  try {
    const text = await navigator.clipboard.readText();
    if (text) {
      inputBcUrl.value = text;
    }
  } catch (e) {
    console.log('Failed to read clipboard', e);
  }
}

/**
 * Constructs the OAuth2 authorization URL for Microsoft Business Central authentication.
 * Includes all required parameters for PKCE flow including client ID, redirect URI,
 * scope, and code challenge.
 *
 * @returns {string} The complete authorization URL with query parameters, or empty string if export types not available
 */
function buildAuthUrl(): string {
  if (!exportTypes.value) {
    return '';
  }
  const params = new URLSearchParams({
    redirect_uri: props.redirectUri,
    client_id: exportTypes.value.clientId,
    response_type: exportTypes.value.authRequestAttributes.response_type,
    response_mode: exportTypes.value.authRequestAttributes.response_mode,
    scope: exportTypes.value
      ? decodeURIComponent(exportTypes.value.authRequestAttributes.scope)
      : '',
    state: 'godesta_' + Math.random().toString(36).substring(2, 10),
    code_challenge: integrationStore.pkceChallenge,
    code_challenge_method: 'S256',
  });
  const authUrl = exportTypes.value.authUrl.replace(
    '${tenantId}',
    props.tenantId,
  );
  return `${authUrl}?${params.toString()}`;
}

/**
 * Opens a centered popup window for Microsoft Business Central authentication.
 * Calculates optimal positioning based on current window dimensions.
 *
 * @param {string} url - The authorization URL to open in the popup
 */
function openPopup(url: string) {
  const width = 600;
  const height = 700;
  const left = window.screenX + (window.outerWidth - width) / 2;
  const top = window.screenY + (window.outerHeight - height) / 2;
  popup.value = window.open(
    url,
    'ms_bc_auth',
    `width=${width},height=${height},left=${left},top=${top}`,
  );
}

/**
 * Sets up polling to monitor the authentication popup for authorization code.
 * Checks popup status every second and handles successful authorization or popup closure.
 * Updates authentication status and triggers token exchange when code is received.
 */
function listenForAuthCode() {
  popupInterval.value = window.setInterval(async () => {
    try {
      if (!popup.value || popup.value.closed) {
        if (popupInterval.value) {
          clearInterval(popupInterval.value);
        }
        loading.value = false;
        msStatus.value = 'failed';
        return;
      }
      const popupUrl = popup.value.location.href;
      if (popupUrl.startsWith(props.redirectUri)) {
        const urlObj = new URL(popupUrl);
        const code = urlObj.searchParams.get('code');
        // const state = urlObj.searchParams.get('state');
        if (code) {
          if (popupInterval.value) {
            clearInterval(popupInterval.value);
          }
          popup.value.close();
          msStatus.value = 'received';
          tokenStatus.value = 'exchanging';
          await handleTokenExchange(code);
        }
      }
    } catch (e) {
      // Ignore cross-origin errors until redirect
    }
  }, 1000);
}

/**
 * Exchanges the OAuth2 authorization code for access and refresh tokens.
 * Creates export user and updates token information in the integration store.
 * Emits connectionSuccessful event on successful token exchange.
 *
 * @param {string} code - The authorization code received from Microsoft Business Central
 */
async function handleTokenExchange(code: string) {
  try {
    loading.value = true;
    tokenStatus.value = 'exchanging';
    // Use store to exchange code for tokens and create export user
    const result = await integrationStore.exchangeCodeForTokens(
      code,
      bcTokenId.value,
      bcEnvironment.value,
    );
    if (result && result.companies) {
      const tokenData = new GetTokensFromOauth2AuthCodeRequest(
        result.exportUser.accessToken,
        result.exportUser.refreshToken,
        result.exportUser.tenantId,
        result.exportUser.environmentName,
      );

      await integrationStore.updateExportUserToken(tokenData);
      tokenStatus.value = 'success';
      emit('connectionSuccessful', result);
    } else {
      tokenStatus.value = 'failed';
    }
  } catch (err) {
    tokenStatus.value = 'failed';
  } finally {
    loading.value = false;
  }
}

/**
 * Initiates the Microsoft Business Central OAuth2 authentication flow.
 * Generates PKCE challenge, builds authorization URL, opens popup, and starts monitoring.
 * Saves the used URL to localStorage for future reference.
 */
async function startAuthFlow() {
  loading.value = true;
  msStatus.value = 'opened';
  tokenStatus.value = 'no token';
  await integrationStore.generatePkce();
  const urlToUse = effectiveBcUrl.value;
  if (!urlToUse) {
    loading.value = false;
    return;
  }
  const authUrl = buildAuthUrl();
  openPopup(authUrl);
  listenForAuthCode();
  // Save to localStorage for next time
  localStorage.setItem('msbc_last_used_url', urlToUse);
  lastUsedBcUrl.value = urlToUse;
}

onMounted(() => {
  const last = localStorage.getItem('msbc_last_used_url');
  if (last) {
    lastUsedBcUrl.value = last;
  }
});

onBeforeUnmount(() => {
  if (popupInterval.value) {
    clearInterval(popupInterval.value);
  }
  if (popup.value && !popup.value.closed) {
    popup.value.close();
  }
});
</script>

<style lang="scss" scoped>
.ms-bc-connect-card {
  border-radius: 12px;
  border: 2px solid var(--border-color);
  padding: 12px 24px 0 24px;
  background: var(--background-color-300);
  transition: all 0.5s;

  &.connected {
    border-color: $toast-success-border;
  }

  .header-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 22px;
    margin-bottom: 12px;
    justify-content: space-between;
    .status-chips {
      display: flex;
      gap: 8px;
      margin-top: 4px;
    }
  }
  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 18px;
    .connected-info-item {
      padding: 12px 0;
      border-radius: 8px;
      background-color: var(--background-color-200);
      .connected-info-label {
        margin-left: 12px;
        display: flex;
        color: $success;
        font-weight: 700;
        font-size: 14px;
      }
    }
    .info-item {
      display: flex;
      flex-direction: column;
      gap: 2px;
      margin-left: 8px;
    }
    .info-label {
      font-size: 0.92em;
      color: var(--light-text-color);
      font-weight: 500;
    }
    .info-value {
      font-size: 1.05em;
      color: var(--text-color);
    }
    .last-url {
      font-size: 0.97em;
      color: #90caf9;
    }
  }
  .form-row {
    display: flex;
    gap: 18px;
    align-items: flex-end;
    margin-bottom: 14px;
    .connect-btn {
      font-weight: 700;
      background: #2563eb !important;

      &:hover {
        box-shadow: var(--box-shadow);
      }
    }
    .paste-btn {
      padding: 4px;
      background-color: var(--background-color-200);
    }
  }
}
</style>
