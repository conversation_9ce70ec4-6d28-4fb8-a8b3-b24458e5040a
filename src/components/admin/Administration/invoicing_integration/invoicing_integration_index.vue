<template>
  <div class="reports-container px-4">
    <div class="top-panel"></div>
    <div class="header">
      <div class="title-container">
        <GTitle
          title="Invoicing Integration"
          subtitle="Manage your accounting system integrations for automated invoice
          processing."
          :divider="false"
        />
      </div>
      <v-spacer></v-spacer>
      <v-btn flat class="v-btn-custom" @click="createNewIntegration">
        <v-icon>fas fa-plus</v-icon>
        New Integration
      </v-btn>
    </div>

    <!-- Main integrations table -->
    <v-data-table
      class="accounting-data-table"
      :headers="tableHeaders"
      :items="sortedIntegrations"
      item-key="id"
      hide-actions
    >
      <template v-slot:items="props">
        <tr @click="editIntegration(props.item)" style="cursor: pointer">
          <td>
            <div class="integration-name">{{ props.item.name }}</div>
            <div class="integration-meta">
              {{ props.item.company }} • {{ props.item.division }}
            </div>
          </td>
          <td>{{ formatExportType(props.item.exportType) }}</td>
          <td style="width: 280px">
            {{ props.item.tenantId }}
          </td>
          <td>{{ props.item.environmentName }}</td>
          <td>{{ props.item.companyName || 'Not set' }}</td>
          <td>
            <span v-if="props.item.startDate || props.item.endDate">
              {{
                props.item.startDate
                  ? returnFormattedDate(props.item.startDate)
                  : '-'
              }}
              <span v-if="props.item.endDate"
                >-
                {{ returnFormattedDate(props.item.endDate) }}
              </span>
            </span>
            <span v-else>-</span>
          </td>
          <td>
            <div class="status-indicator">
              <!-- Remove the status-dot span -->
              <span
                class="status-text"
                :class="
                  isActive(props.item) ? 'status-active' : 'status-inactive'
                "
              >
                {{ isActive(props.item) ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </td>
        </tr>
      </template>
      <template #no-data>
        <div class="empty-message">
          <i class="fas fa-inbox"></i>
          <h4>No integrations configured</h4>
          <p>
            Create your first integration to start syncing invoices with your
            accounting system.
          </p>
        </div>
      </template>
    </v-data-table>

    <!-- Edit/Create Dialog using v-dialog -->
    <v-dialog
      v-model="isDialogOpen"
      :width="'800px'"
      persistent
      class="ma-0"
      content-class="v-dialog-custom"
      no-click-animation
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>{{
          isEditing ? 'Edit Integration' : 'Create New Integration'
        }}</span>
        <div class="app-theme__center-content--closebutton" @click="closeModal">
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>

      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout row wrap class="body-scrollable--75" pa-3>
            <form class="integration-form" @submit.prevent="saveIntegration">
              <!-- Integration Type -->
              <div>
                <div class="form-group">
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="pl-2 pb-0 form-field-required-marker">
                      Integration Type
                    </h6>
                  </v-layout>
                  <!-- Show loading state -->
                  <div v-if="isLoadingExportTypes" class="loading-state mb-3">
                    <v-alert type="info" dense outlined>
                      <v-icon slot="prepend">info</v-icon>
                      <v-progress-circular
                        indeterminate
                        size="16"
                        class="mr-2"
                      ></v-progress-circular>
                      Loading integration configuration...
                    </v-alert>
                  </div>

                  <!-- Show error state -->
                  <div v-if="exportTypesError" class="export-types-error mb-3">
                    <v-alert type="error" dense outlined>
                      <v-icon slot="prepend">error</v-icon>
                      {{ exportTypesError }}
                      <div class="mt-2">
                        <v-btn
                          small
                          outlined
                          color="error"
                          @click="initializeIntegrationData"
                        >
                          Retry
                        </v-btn>
                      </div>
                    </v-alert>
                  </div>

                  <div
                    v-if="
                      !hasAvailableIntegrationTypes &&
                      !isLoadingExportTypes &&
                      !exportTypesError
                    "
                    class="integration-type-warning"
                  >
                    <v-alert type="warning" dense outlined class="mb-3">
                      <v-icon slot="prepend">warning</v-icon>
                      No available integration types. Please contact your
                      administrator to configure integration settings.
                    </v-alert>
                  </div>
                  <v-select
                    v-else
                    id="exportType"
                    v-model="currentIntegration.exportType"
                    :items="dynamicIntegrationTypeOptions"
                    item-value="value"
                    item-text="text"
                    :disabled="isEditing || !hasAvailableIntegrationTypes"
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    label="Integration Type"
                    required
                  />
                </div>
              </div>
              <!-- Business Central Configuration -->
              <div
                v-if="
                  currentIntegration.exportType ===
                  ExportTypeEnum.BUSINESS_CENTRAL
                "
                class="form-section"
              >
                <h6 class="pl-2">Business Central Configuration</h6>
                <MsBusinessCentralConnectCard
                  v-if="!isEditing || !isActive(currentIntegration)"
                  :redirectUri="msBcRedirectUri"
                  :tenantId="currentIntegration.tenantId || 'common'"
                  :isEdit="isEditing"
                  @connectionSuccessful="onMsBcConnectionSuccess"
                />
                <div v-if="authCompleted && !isEditing" class="form-group">
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="pl-2 pb-0 form-field-required-marker">
                      Destination Company
                    </h6>
                  </v-layout>
                  <select
                    id="companySelect"
                    v-model="selectedCompany"
                    class="form-control"
                    required
                  >
                    <option value="">Select a company...</option>
                    <option
                      v-for="(name, id) in availableCompanies"
                      :key="id"
                      :value="{ id, name }"
                    >
                      {{ name }}
                    </option>
                  </select>
                </div>
                <div v-if="isEditing" class="readonly-fields">
                  <div class="form-group">
                    <label>Tenant ID</label>
                    <div class="readonly-value">
                      {{ currentIntegration.tenantId }}
                    </div>
                  </div>
                  <div class="form-group">
                    <label>Environment</label>
                    <div class="readonly-value">
                      {{ currentIntegration.environmentName }}
                    </div>
                  </div>
                  <div class="form-group">
                    <label>Company</label>
                    <div class="readonly-value">
                      {{ currentIntegration.companyName || '-' }}
                    </div>
                  </div>
                  <div class="form-group">
                    <label>Client</label>
                    <div class="readonly-value">
                      {{ currentIntegration.company || '-' }}
                    </div>
                  </div>
                  <div class="form-group">
                    <label>Division</label>
                    <div class="readonly-value">
                      {{ currentIntegration.division }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- Integration Settings -->
              <div>
                <div>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="pl-2 pb-0 form-field-required-marker">
                      Integration Name
                    </h6>
                  </v-layout>
                  <v-text-field
                    id="integrationName"
                    v-model="currentIntegration.name"
                    label="Integration Name"
                    placeholder="e.g., BC_PRODUCTION_01"
                    :disabled="false"
                    required
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                  />
                </div>
                <div class="form-row">
                  <div>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="pl-2 pb-0">Entity Export Type</h6>
                    </v-layout>
                    <v-select
                      id="entityExportType"
                      v-model="currentIntegration.entityExportType"
                      :items="entityExportTypeOptions"
                      item-value="value"
                      item-text="text"
                      class="v-solo-custom"
                      solo
                      flat
                      color="light-blue"
                      label="Entity Export Type"
                      :disabled="false"
                    />
                  </div>
                  <div>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="pl-2 pb-0">Financial Export Type</h6>
                    </v-layout>
                    <v-select
                      id="financialExportType"
                      v-model="currentIntegration.financialExportType"
                      :items="financialExportTypeOptions"
                      item-value="value"
                      item-text="text"
                      class="v-solo-custom"
                      solo
                      flat
                      color="light-blue"
                      label="Financial Export Type"
                      :disabled="false"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="pl-2 pb-0 form-field-required-marker">
                        Start Date
                      </h6>
                    </v-layout>
                    <DatePickerBasic
                      :epochTime="
                        currentIntegration.startDate !== 0
                          ? currentIntegration.startDate
                          : 0
                      "
                      @setEpoch="
                        (epoch) => (currentIntegration.startDate = epoch || 0)
                      "
                      :clearable="true"
                      :hideIcon="false"
                      labelName="Select Start Date"
                      class="v-solo-custom"
                      :solo-input="true"
                      :hideDetails="false"
                      :isRequired="true"
                      :validationErrorMessage="startDateError || undefined"
                    />
                  </div>
                  <div>
                    <v-layout align-center class="form-field-label-container">
                      <h6 class="pl-2 pb-0 form-field-required-marker">
                        End Date
                      </h6>
                    </v-layout>
                    <div class="date-picker-wrapper">
                      <DatePickerBasic
                        :epochTime="
                          !noEndDate && currentIntegration.endDate !== 0
                            ? currentIntegration.endDate
                            : 0
                        "
                        @setEpoch="
                          (epoch) => (currentIntegration.endDate = epoch || 0)
                        "
                        :clearable="true"
                        :hideIcon="false"
                        labelName="Select End Date"
                        class="v-solo-custom"
                        :formDisabled="noEndDate"
                        :solo-input="true"
                        :hideDetails="false"
                        :validationErrorMessage="endDateError || undefined"
                      />
                    </div>
                    <v-checkbox
                      v-model="noEndDate"
                      label="No End Date"
                      class="mt-3"
                      color="info"
                    />
                  </div>
                </div>
              </div>
            </form>
          </v-layout>
        </v-flex>

        <v-flex md12 class="pt-2">
          <v-divider></v-divider>
          <v-layout align-center class="pa-2">
            <v-btn
              color="error"
              outline
              @click="closeModal"
              :disabled="saveInProgress"
            >
              Cancel
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              :loading="saveInProgress"
              :disabled="
                !canSave || saveInProgress || currentIntegration.startDate === 0
              "
              @click="saveIntegration"
              block
            >
              {{
                saveInProgress
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                    ? 'Update Integration'
                    : 'Create Integration'
              }}
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>

    <!-- Duplicate Integration Confirmation Dialog -->
    <v-dialog
      v-model="showDuplicateDialog"
      max-width="500"
      class="ma-0"
      content-class="v-dialog-custom"
      no-click-animation
      persistent
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span> Integration Already Exists </span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeDuplicateDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout row wrap class="body-scrollable--75" pa-3>
            <h3 class="subheader--light">Integration Already Exists</h3>
            <span class="body-text-2 mt-3 mb-2">
              An integration with this tenant/environment/company combination
              already exists. Would you like to open the existing integration
              instead?
            </span>
          </v-layout>
        </v-flex>
        <v-flex md12 class="pt-2">
          <v-divider></v-divider>
          <v-layout align-center class="pa-2">
            <v-btn color="error" outline @click="closeDuplicateDialog">
              Cancel
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="openExistingIntegration">
              Yes, Open Existing
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  getTimestampFromMongoId,
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import {
  CreateExportUserResponse,
  DestinationCompany,
  EntityExportType,
  ExportTypeEnum,
  ExportUser,
  FinancialExportType,
} from '@/interface-models/InvoicingIntegration/ExportUser';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import { computed, onMounted, Ref, ref, watch } from 'vue';
import MsBusinessCentralConnectCard from './ms_business_central_connect.vue';

const integrationStore = useIntegrationStore();

const isDialogOpen: Ref<boolean> = ref(false);
const isEditing: Ref<boolean> = ref(false);
const saveInProgress: Ref<boolean> = ref(false);
const authCompleted: Ref<boolean> = ref(false);
const noEndDate: Ref<boolean> = ref(false);

const availableCompanies: Ref<DestinationCompany[]> = ref([]);
const selectedCompany: Ref<DestinationCompany | null> = ref(null);
const showDuplicateDialog: Ref<boolean> = ref(false);
const duplicateIntegration: Ref<ExportUser | null> = ref(null);

const currentIntegration: Ref<ExportUser> = ref(new ExportUser());

// Static fallback options for integration types
const staticIntegrationTypeOptions = [
  {
    value: ExportTypeEnum.BUSINESS_CENTRAL,
    text: 'Microsoft Business Central',
  },
  {
    value: ExportTypeEnum.QUICKBOOKS,
    text: 'QuickBooks (Coming Soon)',
    disabled: true,
  },
];

// Computed property to check if integration types are available
const hasAvailableIntegrationTypes = computed(() => {
  const exportTypes = integrationStore.exportTypes;
  return exportTypes !== null && exportTypes !== undefined;
});

// Dynamic integration type options based on available exportTypes
const dynamicIntegrationTypeOptions = computed(() => {
  const exportTypes = integrationStore.exportTypes;

  if (!exportTypes) {
    return [];
  }

  // For now, we'll use the static options but this can be extended
  // to dynamically generate options based on exportTypes configuration
  const availableOptions: Array<{
    value: ExportTypeEnum;
    text: string;
    disabled?: boolean;
  }> = [];

  // Check if Business Central is configured
  if (exportTypes.exportType === 'BUSINESS_CENTRAL' || exportTypes.clientId) {
    availableOptions.push({
      value: ExportTypeEnum.BUSINESS_CENTRAL,
      text: 'Microsoft Business Central',
    });
  }

  // Future: Add other integration types based on exportTypes data
  // if (exportTypes.supportsQuickBooks) {
  //   availableOptions.push({
  //     value: ExportTypeEnum.QUICKBOOKS,
  //     text: 'QuickBooks',
  //   });
  // }

  return availableOptions.length > 0
    ? availableOptions
    : staticIntegrationTypeOptions;
});

const entityExportTypeOptions = [
  { value: EntityExportType.PUSH_ALL_ENTITIES, text: 'Push All Entities' },
  { value: EntityExportType.PULL_CUSTOMERS, text: 'Pull Customers' },
  { value: EntityExportType.PULL_ALL_ENTITIES, text: 'Pull All Entities' },
];

const financialExportTypeOptions = [
  {
    value: FinancialExportType.EXPORT_CUSTOMER_INVOICES,
    text: 'Export Customer Invoices',
  },
  {
    value: FinancialExportType.EXPORT_FAO_PAYMENTS,
    text: 'Export FAO Payments',
  },
  {
    value: FinancialExportType.EXPORT_ALL_TRANSACTIONS,
    text: 'Export All Transactions',
  },
];

const tableHeaders = computed<TableHeader[]>(() => [
  {
    text: 'Name',
    value: 'name',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Type',
    value: 'exportType',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Tenant ID',
    value: 'tenantId',
    align: 'left',
    sortable: false,
    width: '280px',
  },
  {
    text: 'Environment',
    value: 'environmentName',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Company Name',
    value: 'companyName',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Date Range',
    value: 'dateRange',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Status',
    value: 'status',
    align: 'left',
    sortable: false,
  },
]);

/**
 * Formats the export type enum value into a human-readable string.
 *
 * @param {ExportTypeEnum} type - The export type enum value
 * @returns {string} The formatted export type name
 */
function formatExportType(type: ExportTypeEnum): string {
  switch (type) {
    case ExportTypeEnum.BUSINESS_CENTRAL:
      return 'Business Central';
    case ExportTypeEnum.QUICKBOOKS:
      return 'QuickBooks';
    default:
      return 'Business Central';
  }
}

/**
 * Validates the start date for an integration based on epoch timestamp.
 * For new integrations, allows any start date. For existing integrations,
 * restricts start date to be no later than the integration creation date.
 */
function validateStartDateEpoch(epochTime: number | null): boolean {
  if (!epochTime || epochTime === 0) {
    return false;
  }
  if (!isEditing.value) {
    return true;
  }
  // Get integration creation date from MongoDB ObjectID
  let integrationCreationDate: Date;
  try {
    integrationCreationDate = getTimestampFromMongoId(
      currentIntegration.value.id || '',
    );
  } catch (error) {
    return false;
  }
  // Get integration creation date at start of day
  const integrationCreationStartOfDay = returnStartOfDayFromEpoch(
    integrationCreationDate.getTime(),
  );
  // Get selected date at start of day
  const selectedDateStartOfDay = returnStartOfDayFromEpoch(epochTime);
  // Only allow dates up to the creation date
  return selectedDateStartOfDay <= integrationCreationStartOfDay;
}

// Validation for start date
const startDateError = computed(() => {
  if (!currentIntegration.value.startDate) {
    return 'Start date is required';
  }
  const isValid = validateStartDateEpoch(currentIntegration.value.startDate);
  if (!isValid) {
    if (isEditing.value) {
      // For editing: get creation date for error message
      try {
        const creationDate = getTimestampFromMongoId(
          currentIntegration.value.id || '',
        );
        return `Start date cannot be after ${creationDate.toLocaleDateString()}, which is when this integration was created.`;
      } catch (error) {
        return 'Invalid start date for this integration.';
      }
    } else {
      return 'Invalid start date.';
    }
  }
  return null;
});

/**
 * Validates the end date for an integration based on epoch timestamp.
 * For new integrations, only validates that end date is after start date.
 * For existing integrations, also ensures end date is not in the past.
 */
function validateEndDateEpoch(epochTime: number | null): boolean {
  if (!epochTime || epochTime === 0) {
    return true; // End date is optional (can be null/0 for "no end date")
  }
  if (!isEditing.value) {
    if (currentIntegration.value.startDate && epochTime) {
      return epochTime > currentIntegration.value.startDate;
    }
    return true;
  }
  // For editing existing integrations: apply validation rules
  // Always enforce that end date cannot be in the past
  const currentMoment = returnTimeNow();
  const selectedEndDate = returnEndOfDayFromEpoch(epochTime);
  if (selectedEndDate < currentMoment) {
    return false;
  }
  if (currentIntegration.value.startDate && epochTime) {
    return epochTime > currentIntegration.value.startDate;
  }
  return true;
}

// Validation for end date
const endDateError = computed(() => {
  if (!currentIntegration.value.endDate) {
    return null;
  }
  const isValid = validateEndDateEpoch(currentIntegration.value.endDate);
  if (!isValid) {
    if (isEditing.value) {
      // Check specific validation failures for better error messages
      const currentMoment = returnTimeNow();
      const selectedEndDate = returnEndOfDayFromEpoch(
        currentIntegration.value.endDate,
      );
      if (selectedEndDate < currentMoment) {
        return 'End date cannot be in the past. Please select today or a future date.';
      }
      if (
        currentIntegration.value.startDate &&
        currentIntegration.value.endDate <= currentIntegration.value.startDate
      ) {
        return 'End date must be after the start date.';
      }
    } else {
      // For new integrations, only check end > start
      if (
        currentIntegration.value.startDate &&
        currentIntegration.value.endDate <= currentIntegration.value.startDate
      ) {
        return 'End date must be after the start date.';
      }
    }
  }
  return null;
});

// Computed property for redirect URI
const msBcRedirectUri = computed(() => {
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/administration/invoicing_integration_callback_ms`;
  }
  return '/administration/invoicing_integration_callback_ms';
});

const canSave = computed(() => {
  if (!currentIntegration.value.name) {
    return false;
  }
  // Check date validation
  const startDateValidation = validateStartDateEpoch(
    currentIntegration.value.startDate,
  );
  const endDateValidation = validateEndDateEpoch(
    currentIntegration.value.endDate,
  );

  if (startDateValidation !== true || endDateValidation !== true) {
    return false;
  }

  if (!isEditing.value) {
    // For create operations, allow save even if there might be duplicates
    // Handled duplicates with the confirmation dialog
    return (
      authCompleted.value &&
      selectedCompany.value &&
      currentIntegration.value.name &&
      currentIntegration.value.exportType
    );
  }
  return true;
});

/**
 * Determines if an integration is currently active based on refresh token expiry.
 * An integration is considered active if it has a valid refresh token that hasn't expired.
 */
function isActive(integration: ExportUser | null): boolean {
  if (!integration || !integration.refreshTokenExpiry) {
    return false;
  }
  return integration.refreshTokenExpiry > Date.now();
}

/**
 * Opens an existing integration for editing in the dialog.
 * Creates a copy of the integration data and sets up the editing state.
 * Handles authorization status based on token expiry.
 *
 * @param {ExportUser} integration - The integration to edit
 */
function editIntegration(integration: ExportUser) {
  if (!integration) {
    showNotification('Error: Invalid integration data', {
      type: HealthLevel.ERROR,
    });
    return;
  }

  try {
    // Create a new ExportUser instance and copy properties
    const newIntegration = new ExportUser();
    currentIntegration.value = Object.assign(newIntegration, integration);

    // // Ensure refreshTokenExpiry (fallback to 0 if not set)
    // if (typeof currentIntegration.value.refreshTokenExpiry !== 'number') {
    //   currentIntegration.value.refreshTokenExpiry = 0;
    // }
    isEditing.value = true;
    isDialogOpen.value = true;
    // If inactive, allow re-authorization
    if (!isActive(currentIntegration.value)) {
      authCompleted.value = false;
    } else {
      authCompleted.value = true;
    }
  } catch (error) {
    console.error('Error in editIntegration:', error);
    showNotification('Error opening integration for editing', {
      type: HealthLevel.ERROR,
    });
  }
}

/**
 * Initializes the dialog for creating a new integration.
 * Resets all form state and opens the creation dialog.
 */
function createNewIntegration() {
  currentIntegration.value = new ExportUser();
  isEditing.value = false;
  authCompleted.value = false;
  isDialogOpen.value = true;
  availableCompanies.value = [];
  selectedCompany.value = null;
  showDuplicateDialog.value = false;
  duplicateIntegration.value = null;
}

/**
 * Closes the integration dialog and resets all related state.
 * Clears form data, company selections, and dialog states.
 */
function closeModal() {
  isDialogOpen.value = false;
  authCompleted.value = false;
  availableCompanies.value = [];
  selectedCompany.value = null;
  showDuplicateDialog.value = false;
  duplicateIntegration.value = null;
}

/**
 * Handles successful Microsoft Business Central connection.
 * Updates the current integration with export user data and available companies.
 * Shows success notification and enables company selection.
 *
 * @param {CreateExportUserResponse} result - The response from successful BC connection
 */
function onMsBcConnectionSuccess(result: CreateExportUserResponse) {
  if (result) {
    currentIntegration.value = Object.assign(
      new ExportUser(),
      result.exportUser,
    );
    availableCompanies.value = result.companies;

    // Call the token refresh API to update token information
    // try {
    //   const tokenData = new GetTokensFromOauth2AuthCodeRequest(
    //     result.exportUser.accessToken,
    //     result.exportUser.refreshToken,
    //     '',
    //     result.exportUser.environmentName,
    //   );

    //   const tokenUpdateResult =
    //     await integrationStore.updateExportUserToken(tokenData);

    //   if (tokenUpdateResult) {
    //     console.log('Token refresh successful:', tokenUpdateResult);
    //     // Update the current integration with the refreshed token data if needed
    //     if (tokenUpdateResult.accessToken) {
    //       currentIntegration.value.accessToken = tokenUpdateResult.accessToken;
    //     }
    //     if (tokenUpdateResult.refreshToken) {
    //       currentIntegration.value.refreshToken =
    //         tokenUpdateResult.refreshToken;
    //     }
    //     if (tokenUpdateResult.accessTokenExpiry) {
    //       currentIntegration.value.accessTokenExpiry =
    //         tokenUpdateResult.accessTokenExpiry;
    //     }
    //     if (tokenUpdateResult.refreshTokenExpiry) {
    //       currentIntegration.value.refreshTokenExpiry =
    //         tokenUpdateResult.refreshTokenExpiry;
    //     }

    //     // If we're editing an existing integration, refresh the integrations list
    //     if (isEditing.value) {
    //       await integrationStore.fetchIntegrations();
    //     }
    //   } else {
    //     console.warn('Token refresh failed, but connection was successful');
    //     showNotification(
    //       'Connection successful, but token refresh failed. Please try reconnecting if you experience issues.',
    //       {
    //         type: HealthLevel.WARNING,
    //       },
    //     );
    //   }
    // } catch (error) {
    //   console.error('Error during token refresh:', error);
    //   showNotification(
    //     'Connection successful, but token refresh failed. Please try reconnecting if you experience issues.',
    //     {
    //       type: HealthLevel.WARNING,
    //     },
    //   );
    // }

    authCompleted.value = true;
    showNotification('Authorization successful! Please select a company.', {
      type: HealthLevel.SUCCESS,
    });
  } else {
    authCompleted.value = false;
    showNotification('Failed to authorize with Microsoft Business Central.', {
      type: HealthLevel.ERROR,
    });
  }
}

/**
 * Closes the duplicate integration confirmation dialog and resets related state.
 */
function closeDuplicateDialog() {
  showDuplicateDialog.value = false;
  duplicateIntegration.value = null;
}

/**
 * Opens an existing duplicate integration for editing instead of creating a new one.
 * Validates the integration data, closes dialogs, and switches to edit mode.
 * Shows error notifications if the integration data is invalid.
 */
function openExistingIntegration() {
  if (duplicateIntegration.value) {
    const integrationToEdit = duplicateIntegration.value;

    if (!integrationToEdit.id) {
      showNotification('Error: Invalid integration data', {
        type: HealthLevel.ERROR,
      });
      closeDuplicateDialog();
      return;
    }

    closeDuplicateDialog();
    closeModal();

    editIntegration(integrationToEdit);
  } else {
    showNotification('Error: Could not find existing integration', {
      type: HealthLevel.ERROR,
    });
    closeDuplicateDialog();
  }
}

/**
 * Saves the current integration (create or update operation).
 * Validates required fields, checks for duplicates, and handles the save operation.
 * Shows appropriate notifications and refreshes the integrations list on success.
 * For create operations, shows duplicate confirmation dialog if needed.
 */
async function saveIntegration() {
  try {
    saveInProgress.value = true;

    // Only set company info from dropdown in create mode
    if (!isEditing.value && selectedCompany.value) {
      currentIntegration.value.companyId = selectedCompany.value.id;
      currentIntegration.value.companyName = selectedCompany.value.name;
    }

    // Check for duplicate
    const companyId = isEditing.value
      ? currentIntegration.value.companyId
      : selectedCompany.value?.id || '';

    if (
      !currentIntegration.value.tenantId ||
      !currentIntegration.value.environmentName ||
      !companyId
    ) {
      showNotification(
        'Missing one of the required fields: Tenant ID, Environment Name or Company',
        {
          type: HealthLevel.ERROR,
        },
      );
      return;
    }

    const currentUniqueApiReference = `${currentIntegration.value.tenantId}${currentIntegration.value.environmentName}${companyId}`;

    const foundDuplicate = integrationStore.integrations.find((integration) => {
      // Skip the current integration if we're editing
      if (isEditing.value && integration.id === currentIntegration.value.id) {
        return false;
      }

      const existingUniqueApiReference = `${integration.tenantId}${integration.environmentName}${integration.companyId}`;
      return existingUniqueApiReference === currentUniqueApiReference;
    });

    // If duplicate found, only show confirmation dialog for NEW integrations
    if (foundDuplicate && !isEditing.value) {
      duplicateIntegration.value = foundDuplicate;
      showDuplicateDialog.value = true;
      return;
    }

    const result = await useIntegrationStore().saveIntegration(
      currentIntegration.value,
    );

    if (result) {
      await useIntegrationStore().fetchIntegrations();
      closeModal();
      showNotification(
        isEditing.value
          ? 'Integration updated successfully'
          : 'Integration created successfully',
        { type: HealthLevel.SUCCESS },
      );
    } else {
      throw new Error('Failed to save integration');
    }
  } catch (error) {
    console.error('Save failed:', error);
    showNotification('Failed to save integration', { type: HealthLevel.ERROR });
  } finally {
    saveInProgress.value = false;
  }
}

// Sort integrations with active first
const sortedIntegrations = computed(() => {
  return [...integrationStore.integrations].reverse();
});

// Watcher to clear endDate if noEndDate is checked
watch(noEndDate, (val) => {
  if (val) {
    currentIntegration.value.endDate = 0;
  }
});

// Loading states and error handling
const isLoadingExportTypes = computed(() => integrationStore.isLoading);
const exportTypesError = ref<string | null>(null);

// Enhanced initialization with error handling
async function initializeIntegrationData() {
  try {
    exportTypesError.value = null;
    await integrationStore.fetchExportTypes();
    await integrationStore.fetchIntegrations();

    // Validate that we have the necessary configuration
    if (!integrationStore.exportTypes) {
      exportTypesError.value =
        'Failed to load integration configuration. Please contact your administrator.';
    }
  } catch (error) {
    console.error('Error initializing integration data:', error);
    exportTypesError.value =
      'Failed to load integration data. Please refresh the page and try again.';
  }
}

onMounted(() => {
  initializeIntegrationData();
});
</script>

<style lang="scss" scoped>
.reports-container {
  padding-top: 68px;
  background-color: $app-dark-primary-250;
}
.header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-color);
}

.v-btn-custom {
  border-radius: $border-radius-base;
  color: white;
  background-color: var(--info);
  .v-icon {
    margin-right: 14px;
    font-size: 14px;
  }
}

.integration-meta {
  color: var(--light-text-color);
}

.integration-form {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: $border-radius-Xsm;
  font-size: $font-size-14;
  font-family: $app-font-family;
  transition: border-color 0.2s;
  background: var(--background-color-300);
  color: var(--text-color);
  &:focus {
    outline: none;
    border-color: $primary;
    box-shadow: 0 0 0 3px $highlight-text-button;
  }
  &:disabled {
    background: var(--background-color-400);
    color: $light-text-color;
    cursor: not-allowed;
  }
}

.readonly-fields {
  background: var(--background-color-300);
  border-radius: $border-radius-sm;
  padding: 16px;
  margin-top: 16px;
  .form-group {
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .readonly-value {
    font-size: $font-size-13;
    color: var(--light-text-color);
    background: var(--background-color-card);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;

  .status-text {
    font-size: $font-size-14;
    font-weight: 500;
    color: inherit;
  }
  .status-active {
    color: $success;
  }
  .status-inactive {
    color: $error;
  }
}
</style>
