<template>
  <v-layout class="client-details-integrations" wrap>
    <v-flex md8 offset-md2 class="">
      <v-layout mt-3 row wrap>
        <v-flex md12 pb-1
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">
              Electronic Data Interchange
            </h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">FTP Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientDetails.importTransformationType"
                      :items="importUserList"
                      item-text="displayName"
                      item-value="username"
                      class="v-solo-custom"
                      solo
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ImportUserSelect :clientDetails="clientDetails" />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">API Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientsApiUsers"
                      :items="apiUserList"
                      class="v-solo-custom"
                      solo
                      multiple
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ApiUserSelect
                      :clientDetails="clientDetails"
                      @setClientsApiUsers="setClientsApiUsers"
                    />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <!-- Export Associations Section -->
    <v-flex md8 offset-md2>
      <v-layout mt-3 row wrap>
        <v-flex md12 pb-1>
          <v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Data Export Associations</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>

        <!-- Export Associations Table -->
        <v-flex md12 v-if="exportAssociationsWithDetails.length > 0">
          <div class="table-header-left-action-icon-container">
            <v-checkbox
              @click.stop
              :indeterminate="associationsPartiallySelected"
              v-model="allAssociationsSelected"
              hide-details
              :ripple="false"
              :disabled="exportAssociationsWithDetails.length === 0"
              color="info"
            ></v-checkbox>
          </div>
          <v-data-table
            :headers="exportAssociationHeaders"
            :items="exportAssociationsWithDetails"
            item-key="uniqueApiReference"
            hide-actions
            class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          >
            <template v-slot:items="item">
              <tr>
                <td class="inner-table__cell checkbox-type">
                  <v-checkbox
                    @click.stop
                    v-model="item.item.isSelected"
                    hide-details
                    :ripple="false"
                    color="info"
                  ></v-checkbox>
                </td>
                <td class="inner-table__cell">
                  {{ item.item.integrationName }}
                </td>
                <td class="inner-table__cell">
                  {{ item.item.exportUserName }}
                </td>
                <td class="inner-table__cell">
                  {{ item.item.remoteEntityReference }}
                </td>
                <td class="inner-table__cell">{{ item.item.customerName }}</td>
                <td class="inner-table__cell">
                  {{ item.item.customerNumber }}
                </td>
                <td class="inner-table__cell">
                  {{ item.item.customerAddress }}
                </td>
              </tr>
            </template>
          </v-data-table>

          <!-- Action buttons for selected associations -->
          <v-layout mt-2 justify-end>
            <v-btn
              color="error"
              outlined
              :disabled="!hasSelectedAssociations"
              @click="showRemoveAssociationsDialog = true"
            >
              Remove Selected
            </v-btn>
          </v-layout>
        </v-flex>

        <!-- No associations message -->
        <v-flex md12 v-else>
          <v-card class="pa-3">
            <v-card-text class="text-center">
              No export associations configured for this client.
            </v-card-text>
          </v-card>
        </v-flex>

        <!-- Add Association Button -->
        <v-flex md12 mt-3>
          <v-layout justify-end>
            <a
              href="#"
              @click.prevent="openAddAssociationDialog"
              class="text-decoration-none"
            >
              Add Export Association
            </a>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <!-- Remove Associations Confirmation Dialog -->
    <GDialog
      v-if="showRemoveAssociationsDialog"
      title="Remove Export Associations"
      :isActionable="true"
      :isDelete="true"
      deleteBtnText="Remove"
      @confirm="removeSelectedAssociations"
      @cancel="showRemoveAssociationsDialog = false"
      @delete="removeSelectedAssociations"
    >
      <p>
        Are you sure you want to remove the selected export associations? This
        action cannot be undone.
      </p>
      <ul>
        <li
          v-for="association in selectedAssociations"
          :key="association.uniqueApiReference"
        >
          {{ association.integrationName }} - {{ association.customerName }}
        </li>
      </ul>
    </GDialog>

    <!-- Add Association Dialog -->
    <GDialog
      v-if="showAddAssociationDialog"
      title="Add Export Association"
      width="800px"
      :isActionable="true"
      confirmBtnText="Add Association"
      :confirmDisabled="!selectedCustomer || !selectedExportUser"
      @confirm="addNewAssociation"
      @cancel="closeAddAssociationDialog"
    >
      <v-tabs v-model="activeTab" color="primary">
        <v-tab key="select">Select Customer</v-tab>
        <v-tab key="details" :disabled="!selectedCustomer"
          >Review Details</v-tab
        >

        <v-tab-item key="select">
          <v-layout column class="pa-3">
            <!-- Export User Selection -->
            <v-flex mb-3>
              <v-select
                label="Select Export Integration"
                v-model="selectedExportUser"
                :items="availableExportUsers"
                item-text="displayName"
                item-value="uniqueApiReference"
                :disabled="availableExportUsers.length === 0"
                class="v-solo-custom"
                solo
                color="light-blue"
                flat
              >
                <template v-slot:no-data>
                  <v-list-tile>
                    <v-list-tile-content>
                      <v-list-tile-title>
                        No export integrations available with customer pull
                        capability
                      </v-list-tile-title>
                    </v-list-tile-content>
                  </v-list-tile>
                </template>
              </v-select>
            </v-flex>

            <!-- Customer Search and Selection -->
            <v-flex v-if="selectedExportUser">
              <v-layout column>
                <v-flex mb-2>
                  <v-btn
                    color="primary"
                    outlined
                    @click="refreshCustomerList"
                    :loading="isLoadingCustomers"
                  >
                    Refresh Customer List
                  </v-btn>
                </v-flex>

                <v-flex>
                  <v-text-field
                    label="Search customers..."
                    v-model="customerSearchQuery"
                    prepend-inner-icon="fas fa-search"
                    clearable
                    class="v-solo-custom"
                    solo
                    flat
                  ></v-text-field>
                </v-flex>

                <!-- Customer List -->
                <v-flex v-if="filteredCustomers.length > 0">
                  <v-data-table
                    :headers="customerSelectionHeaders"
                    :items="filteredCustomers"
                    item-key="id"
                    hide-actions
                    :rows-per-page-items="[10, 25, 50]"
                    class="default-table-dark client-invoice-accounting-table gd-dark-theme"
                    style="max-height: 400px; overflow-y: auto"
                  >
                    <template v-slot:items="item">
                      <tr
                        @click="selectCustomer(item.item)"
                        :class="{
                          'selected-row': selectedCustomer?.id === item.item.id,
                        }"
                      >
                        <td class="inner-table__cell">
                          <v-radio-group
                            v-model="selectedCustomer"
                            hide-details
                          >
                            <v-radio
                              :value="item.item"
                              color="primary"
                            ></v-radio>
                          </v-radio-group>
                        </td>
                        <td class="inner-table__cell">
                          {{ item.item.number }}
                        </td>
                        <td class="inner-table__cell">{{ item.item.name }}</td>
                        <td class="inner-table__cell">
                          {{ formatCustomerAddress(item.item) }}
                        </td>
                        <td class="inner-table__cell">
                          {{ item.item.phone }}
                        </td>
                        <td class="inner-table__cell">
                          {{ item.item.email }}
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-flex>

                <v-flex v-else-if="!isLoadingCustomers && selectedExportUser">
                  <v-card class="pa-3">
                    <v-card-text class="text-center">
                      No customers available. Click "Refresh Customer List" to
                      load customers from the export service.
                    </v-card-text>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-tab-item>

        <v-tab-item key="details">
          <v-layout
            column
            class="pa-3"
            v-if="selectedCustomer && selectedExportUserDetails"
          >
            <v-flex>
              <h6 class="subheader--bold mb-3">Association Details</h6>

              <v-layout row wrap>
                <v-flex md6>
                  <v-card class="pa-3 mr-2">
                    <v-card-title class="subheader--bold"
                      >Export Integration</v-card-title
                    >
                    <v-card-text>
                      <p>
                        <strong>Name:</strong>
                        {{ selectedExportUserDetails.name }}
                      </p>
                      <p>
                        <strong>Type:</strong>
                        {{ selectedExportUserDetails.exportType }}
                      </p>
                      <p>
                        <strong>Company:</strong>
                        {{ selectedExportUserDetails.companyName }}
                      </p>
                    </v-card-text>
                  </v-card>
                </v-flex>

                <v-flex md6>
                  <v-card class="pa-3">
                    <v-card-title class="subheader--bold"
                      >Customer Details</v-card-title
                    >
                    <v-card-text>
                      <p>
                        <strong>Number:</strong> {{ selectedCustomer.number }}
                      </p>
                      <p><strong>Name:</strong> {{ selectedCustomer.name }}</p>
                      <p>
                        <strong>Address:</strong>
                        {{ formatCustomerAddress(selectedCustomer) }}
                      </p>
                      <p>
                        <strong>Phone:</strong> {{ selectedCustomer.phone }}
                      </p>
                      <p>
                        <strong>Email:</strong> {{ selectedCustomer.email }}
                      </p>
                    </v-card-text>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-tab-item>
      </v-tabs>
    </GDialog>
  </v-layout>
</template>

<script setup lang="ts">
import ApiUserSelect from '@/components/admin/ClientDetails/components/api_user_select.vue';
import ImportUserSelect from '@/components/admin/ClientDetails/components/import_user_select.vue';
import GDialog from '@/components/common/ui-elements/g_dialog.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ImportUser from '@/interface-models/Generic/ImportUser';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import {
  CustomerData,
  ExportIntegrations,
} from '@/interface-models/InvoicingIntegration/CustomerDataSnapshot';
import {
  EntityExportType,
  ExportUser,
} from '@/interface-models/InvoicingIntegration/ExportUser';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import { computed, ref } from 'vue';

// Enhanced interface for display purposes
interface ExportAssociationWithDetails extends ExportIntegrations {
  isSelected?: boolean;
  integrationName: string;
  exportUserName: string;
  customerName: string;
  customerNumber: string;
  customerAddress: string;
}

// Enhanced interface for export user display
interface ExportUserDisplay extends ExportUser {
  displayName: string;
}

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const dataImportStore = useDataImportStore();
const integrationStore = useIntegrationStore();

// Existing data
const clientsApiUsers = ref<string[]>([]);

// Export Associations data
const showRemoveAssociationsDialog = ref(false);
const showAddAssociationDialog = ref(false);
const activeTab = ref(0);
const selectedExportUser = ref<string | null>(null);
const selectedCustomer = ref<CustomerData | null>(null);
const customerSearchQuery = ref('');
const isLoadingCustomers = ref(false);
const availableCustomers = ref<CustomerData[]>([]);

// Table headers for export associations
const exportAssociationHeaders = ref<TableHeader[]>([
  {
    text: '',
    value: 'checkbox',
    align: 'center',
    sortable: false,
    width: '50px',
  },
  {
    text: 'Integration',
    value: 'integrationName',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Export User',
    value: 'exportUserName',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Remote Reference',
    value: 'remoteEntityReference',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Customer Name',
    value: 'customerName',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Customer Number',
    value: 'customerNumber',
    align: 'left',
    sortable: true,
  },
  { text: 'Address', value: 'customerAddress', align: 'left', sortable: false },
]);

// Table headers for customer selection
const customerSelectionHeaders = ref<TableHeader[]>([
  {
    text: '',
    value: 'select',
    align: 'center',
    sortable: false,
    width: '50px',
  },
  { text: 'Number', value: 'number', align: 'left', sortable: true },
  { text: 'Name', value: 'name', align: 'left', sortable: true },
  { text: 'Address', value: 'address', align: 'left', sortable: false },
  { text: 'Phone', value: 'phone', align: 'left', sortable: false },
  { text: 'Email', value: 'email', align: 'left', sortable: false },
]);

// A list of all available import (FTP) users
const importUserList = computed<ImportUser[]>(() => {
  return dataImportStore.importUserList;
});

// A list of all available api users
const apiUserList = computed<string[]>(() => {
  return dataImportStore.apiUserList;
});

// Get export users that have customer pull capability
const availableExportUsers = computed<ExportUserDisplay[]>(() => {
  return integrationStore.integrations
    .filter(
      (user: ExportUser) =>
        user.entityExportType === EntityExportType.PULL_CUSTOMERS ||
        user.entityExportType === EntityExportType.PULL_ALL_ENTITIES,
    )
    .map((user: ExportUser) => ({
      ...user,
      displayName: `${user.name} (${user.exportType})`,
    }));
});

// Get selected export user details
const selectedExportUserDetails = computed<ExportUser | null>(() => {
  if (!selectedExportUser.value) {
    return null;
  }
  return (
    integrationStore.integrations.find(
      (user: ExportUser) =>
        user.uniqueApiReference === selectedExportUser.value,
    ) || null
  );
});

// Enhanced export associations with customer details
const exportAssociationsWithDetails = computed<ExportAssociationWithDetails[]>(
  () => {
    if (!props.clientDetails.exportAssociations) {
      return [];
    }

    return props.clientDetails.exportAssociations.map(
      (association: ExportIntegrations) => {
        const exportUser = integrationStore.integrations.find(
          (user: ExportUser) =>
            user.uniqueApiReference === association.uniqueApiReference,
        );

        // Find customer details from available customers
        const customer = availableCustomers.value.find(
          (cust: CustomerData) => cust.id === association.remoteEntityReference,
        );

        return {
          ...association,
          isSelected: false,
          integrationName: exportUser ? `${exportUser.exportType}` : 'Unknown',
          exportUserName: exportUser ? exportUser.name : 'Unknown',
          customerName: customer ? customer.name : 'Unknown Customer',
          customerNumber: customer ? customer.number : 'Unknown',
          customerAddress: customer
            ? formatCustomerAddress(customer)
            : 'Unknown Address',
        };
      },
    );
  },
);

// Checkbox selection logic
const allAssociationsSelected = computed({
  get: () => {
    return (
      exportAssociationsWithDetails.value.length > 0 &&
      exportAssociationsWithDetails.value.every((item) => item.isSelected)
    );
  },
  set: (value: boolean) => {
    exportAssociationsWithDetails.value.forEach((item) => {
      item.isSelected = value;
    });
  },
});

const associationsPartiallySelected = computed(() => {
  const selected = exportAssociationsWithDetails.value.filter(
    (item) => item.isSelected,
  );
  return (
    selected.length > 0 &&
    selected.length < exportAssociationsWithDetails.value.length
  );
});

const hasSelectedAssociations = computed(() => {
  return exportAssociationsWithDetails.value.some((item) => item.isSelected);
});

const selectedAssociations = computed(() => {
  return exportAssociationsWithDetails.value.filter((item) => item.isSelected);
});

// Filtered customers based on search
const filteredCustomers = computed(() => {
  if (!customerSearchQuery.value) {
    return availableCustomers.value;
  }

  const query = customerSearchQuery.value.toLowerCase();
  return availableCustomers.value.filter(
    (customer: CustomerData) =>
      customer.name.toLowerCase().includes(query) ||
      customer.number.toLowerCase().includes(query) ||
      customer.email.toLowerCase().includes(query) ||
      customer.addressLine1.toLowerCase().includes(query),
  );
});

// Helper function to format customer address
const formatCustomerAddress = (customer: CustomerData): string => {
  const parts = [
    customer.addressLine1,
    customer.addressLine2,
    customer.city,
    customer.state,
    customer.postcode,
  ].filter(Boolean);
  return parts.join(', ');
};

// Dialog management methods
const openAddAssociationDialog = async () => {
  showAddAssociationDialog.value = true;
  activeTab.value = 0;
  selectedExportUser.value = null;
  selectedCustomer.value = null;
  customerSearchQuery.value = '';

  // Load integrations if not already loaded
  if (integrationStore.integrations.length === 0) {
    await integrationStore.fetchIntegrations();
  }
};

const closeAddAssociationDialog = () => {
  showAddAssociationDialog.value = false;
  activeTab.value = 0;
  selectedExportUser.value = null;
  selectedCustomer.value = null;
  customerSearchQuery.value = '';
  availableCustomers.value = [];
};

// Customer management methods
const refreshCustomerList = async () => {
  if (!selectedExportUser.value) {
    return;
  }

  try {
    isLoadingCustomers.value = true;
    const customerData =
      await integrationStore.getCustomerListFromExportServices();

    if (customerData && customerData.customers) {
      availableCustomers.value = customerData.customers;
    } else {
      availableCustomers.value = [];
      showNotification('No customers found from export service', {
        title: 'Export Associations',
        type: HealthLevel.WARNING,
      });
    }
  } catch (error) {
    console.error('Error fetching customers:', error);
    showNotification('Failed to fetch customers from export service', {
      title: 'Export Associations',
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoadingCustomers.value = false;
  }
};

const selectCustomer = (customer: CustomerData) => {
  selectedCustomer.value = customer;
  activeTab.value = 1; // Switch to details tab
};

// Association management methods
const addNewAssociation = () => {
  if (!selectedCustomer.value || !selectedExportUser.value) {
    return;
  }

  const newAssociation: ExportIntegrations = {
    uniqueApiReference: selectedExportUser.value,
    remoteEntityReference: selectedCustomer.value.id,
  };

  // Check if association already exists
  const existingAssociation = props.clientDetails.exportAssociations.find(
    (assoc: ExportIntegrations) =>
      assoc.uniqueApiReference === newAssociation.uniqueApiReference &&
      assoc.remoteEntityReference === newAssociation.remoteEntityReference,
  );

  if (existingAssociation) {
    showNotification('This export association already exists', {
      title: 'Export Associations',
      type: HealthLevel.WARNING,
    });
    return;
  }

  // Add the new association
  props.clientDetails.exportAssociations.push(newAssociation);

  showNotification('Export association added successfully', {
    title: 'Export Associations',
    type: HealthLevel.SUCCESS,
  });

  closeAddAssociationDialog();
};

const removeSelectedAssociations = () => {
  const selectedIds = selectedAssociations.value.map((assoc) => ({
    uniqueApiReference: assoc.uniqueApiReference,
    remoteEntityReference: assoc.remoteEntityReference,
  }));

  // Remove selected associations from client details
  props.clientDetails.exportAssociations =
    props.clientDetails.exportAssociations.filter(
      (assoc: ExportIntegrations) =>
        !selectedIds.some(
          (selected) =>
            selected.uniqueApiReference === assoc.uniqueApiReference &&
            selected.remoteEntityReference === assoc.remoteEntityReference,
        ),
    );

  showNotification(`Removed ${selectedIds.length} export association(s)`, {
    title: 'Export Associations',
    type: HealthLevel.SUCCESS,
  });

  showRemoveAssociationsDialog.value = false;
};

// $emit from api_user_select. Contains the clients associated api username list
const setClientsApiUsers = (usernames: string[]) => {
  clientsApiUsers.value = usernames;
};
</script>
