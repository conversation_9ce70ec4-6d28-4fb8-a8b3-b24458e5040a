import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { CustomerDataSnapshot } from '@/interface-models/InvoicingIntegration/CustomerDataSnapshot';
import { ExportTypeConfig } from '@/interface-models/InvoicingIntegration/ExportTypeConfig';
import {
  CreateExportUserResponse,
  EntityExportType,
  ExportUser,
} from '@/interface-models/InvoicingIntegration/ExportUser';
import GetTokensFromOauth2AuthCodeRequest from '@/interface-models/InvoicingIntegration/GetTokensFromOauth2AuthCodeRequest';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';
import { computed, Ref, ref } from 'vue';

export const useIntegrationStore = defineStore('integration', () => {
  const integrations: Ref<ExportUser[]> = ref([]);
  const isLoading: Ref<boolean> = ref(false);

  // PKCE state
  const pkceCode = ref('');
  const pkceChallenge = ref('');

  const exportTypes = ref<ExportTypeConfig | null>(null);

  /**
   * Fetches export type configurations from the server.
   * Retrieves available export types and their configuration settings
   * for setting up integrations with external systems.
   */
  async function fetchExportTypes() {
    try {
      isLoading.value = true;

      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/export/getExportTypes', '', true),
        'getExportTypes',
      );

      if (Array.isArray(result) && result.length > 0) {
        exportTypes.value = result[0] as ExportTypeConfig;
      } else {
        exportTypes.value = null;
      }
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Fetches all integrations (export users) configured for the current organization.
   * Retrieves the list of existing integrations that can be edited or managed.
   */
  async function fetchIntegrations() {
    try {
      isLoading.value = true;
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/export/getExportUsersForEditing', '', true),
        'getExportUsersForEditing',
      );
      if (result) {
        integrations.value = Object.values(result) as ExportUser[];
      } else {
        integrations.value = [];
      }
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Saves an integration (export user) configuration to the server.
   * Handles both creating new integrations and updating existing ones.
   *
   * @param {ExportUser} exportUser - The integration configuration to save
   * @returns {Promise<ExportUser | null>} The save operation result
   */
  async function saveIntegration(
    exportUser: ExportUser,
  ): Promise<ExportUser | null> {
    try {
      isLoading.value = true;
      const payload = { ...exportUser };
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/export/updateExportUser', payload, true),
        'updateExportUser',
      );
      return result;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Updates the token information for an existing export user.
   * This method specifically handles updating token-related properties after a successful connection.
   * @param tokenData - GetTokensFromOauth2AuthCodeRequest containing the token properties to update
   * @returns Promise<ExportUser | null> - The update response or null if failed
   */
  async function updateExportUserToken(
    tokenData: GetTokensFromOauth2AuthCodeRequest,
  ) {
    try {
      isLoading.value = true;
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/export/updateExportUserToken', tokenData, true),
        'updateExportUserToken',
      );
      return result;
    } catch (error) {
      console.error('Error updating export user token:', error);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Generates PKCE (Proof Key for Code Exchange) code verifier and challenge.
   * Creates cryptographically secure random values required for OAuth2 PKCE flow
   * to prevent authorization code interception attacks.
   */
  async function generatePkce() {
    function dec2hex(dec: number) {
      return ('0' + dec.toString(16)).substr(-2);
    }
    function generateRandomString() {
      const array = new Uint32Array(56 / 2);
      window.crypto.getRandomValues(array);
      return Array.from(array, dec2hex).join('');
    }
    function sha256(plain: string) {
      const encoder = new TextEncoder();
      const data = encoder.encode(plain);
      return window.crypto.subtle.digest('SHA-256', data);
    }
    function base64urlencode(a: ArrayBuffer) {
      let str = '';
      const bytes = new Uint8Array(a);
      const len = bytes.byteLength;
      for (let i = 0; i < len; i++) {
        str += String.fromCharCode(bytes[i]);
      }
      return btoa(str)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    }
    async function challenge_from_verifier(v: string) {
      const hashed = await sha256(v);
      return base64urlencode(hashed);
    }
    const code = generateRandomString();
    const challenge = await challenge_from_verifier(code);
    pkceCode.value = code;
    pkceChallenge.value = challenge;
  }

  /**
   * Exchanges OAuth2 authorization code for access and refresh tokens.
   * Performs the token exchange with Microsoft Business Central using PKCE flow,
   * then creates an export user with the received tokens and company information.
   *
   * @param authCode - The authorization code received from OAuth2 flow
   * @param bcTokenId - The Business Central tenant ID
   * @param bcEnvironment - The Business Central environment name
   * @returns Promise containing export user and company data, or null if failed
   */
  async function exchangeCodeForTokens(
    authCode: string,
    bcTokenId: string,
    bcEnvironment: string,
  ): Promise<CreateExportUserResponse | null> {
    function convert_js_urlencoded(jsonData: Record<string, string>): string {
      const urlEncodedString = Object.entries(jsonData)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      return urlEncodedString;
    }

    if (bcTokenId === '' || bcEnvironment === '') {
      throw new Error(
        "You can't proceed without a valid tokenId and environment name",
      );
    }

    if (!pkceCode.value) {
      throw new Error(
        'No pkce_code value found in store, so we cannot make a code verified request to MS.',
      );
    }

    // Required POST data
    const godesta_app_id = exportTypes.value?.appId ?? '';
    const godesta_client_id = exportTypes.value?.clientId ?? '';
    const godesta_client_secret = exportTypes.value?.clientSecret ?? '';
    const godesta_redirect_uri = exportTypes.value
      ? decodeURIComponent(exportTypes.value.authRequestAttributes.redirect_uri)
      : '';

    const postData = {
      client_id: godesta_client_id,
      code: authCode,
      redirect_uri: godesta_redirect_uri,
      grant_type: 'authorization_code',
      code_verifier: pkceCode.value,
    };

    const ms_token_link = exportTypes.value
      ? exportTypes.value?.tokenUrl.replace('${tenantId}', 'common')
      : '';

    const qb_basicauthtoken =
      'Basic ' + btoa(godesta_client_id + ':' + godesta_client_secret);

    const qb_headers = new Headers();
    if (exportTypes.value?.tokenPostData) {
      for (const [key, value] of Object.entries(
        exportTypes.value.tokenPostData,
      )) {
        const resolvedValue = value.replace(
          '${clientId}',
          exportTypes.value.clientId,
        );
        qb_headers.append(key, resolvedValue);
      }
    }

    const request = new Request(ms_token_link, {
      method: 'POST',
      headers: qb_headers,
      mode: 'cors',
      body: convert_js_urlencoded(postData),
    });

    const response = await fetch(request);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    let access_token = '';
    let refresh_token = '';

    if (
      data.hasOwnProperty('access_token') &&
      data.hasOwnProperty('refresh_token')
    ) {
      access_token = data['access_token'];
      refresh_token = data['refresh_token'];
    } else {
      throw new Error(
        'Invalid response from Microsoft: missing access_token or refresh_token',
      );
    }

    // Send request over websocket to create export user
    // The backend will handle checking for existing users and updating tokens as needed
    const getTokensFromOauth2AuthCodePayload =
      new GetTokensFromOauth2AuthCodeRequest(
        access_token,
        refresh_token,
        bcTokenId,
        bcEnvironment,
      );

    const result = await sendRequestAndListenForResponse(
      new WebSocketRequest(
        '/export/createExportUser',
        getTokensFromOauth2AuthCodePayload,
        true,
      ),
      'createExportUser',
    );

    return result;
  }

  /**
   * Finds ExportUser configurations that support customer pulling for the current company/division
   * @returns ExportUser[] - Array of export users with customer pull capabilities
   */
  const getCustomerPullExportUsers = computed(() => {
    const company = sessionManager.getCompanyId();
    const division = sessionManager.getDivisionId();

    return integrations.value.filter(
      (exportUser) =>
        exportUser.company === company &&
        exportUser.division === division &&
        (exportUser.entityExportType === EntityExportType.PULL_CUSTOMERS ||
          exportUser.entityExportType === EntityExportType.PULL_ALL_ENTITIES),
    );
  });

  /**
   * Checks if customer search functionality should be available
   * @returns boolean - True if there are export users with customer pull capabilities
   */
  const hasCustomerPullCapability = computed(() => {
    return getCustomerPullExportUsers.value.length > 0;
  });

  /**
   * Gets the primary export user for customer pulling (first available)
   * @returns ExportUser | null - The primary export user or null if none available
   */
  const getPrimaryCustomerPullExportUser = computed(() => {
    const customerPullUsers = getCustomerPullExportUsers.value;
    return customerPullUsers.length > 0 ? customerPullUsers[0] : null;
  });

  /**
   * Fetches the customer list from export services.
   * @param exportUserId - The ID of the export user to fetch customers for
   * @returns Promise<CustomerListResponse | null> - The customer list response or null if failed
   */
  async function getCustomerListFromExportServices(): Promise<CustomerDataSnapshot | null> {
    try {
      const request = new WebSocketRequest(
        '/export/getCustomerListFromExportServices',
        '',
        true,
      );

      const result = await sendRequestAndListenForResponse(
        request,
        'getCustomerListFromExportServices',
      );

      return result as CustomerDataSnapshot;
    } catch (error) {
      console.error(
        'Error fetching customer list from export services:',
        error,
      );
      return null;
    }
  }

  /**
   * Refreshes the customer list from Business Central.
   * Forces a fresh pull of customer data from the external system.
   * @param request - The refresh request containing exportUserId and optional forceRefresh flag
   * @returns Promise<RefreshCustomerListResponse | null> - The refresh response or null if failed
   */
  async function refreshCustomerListFromExportServices(): Promise<CustomerDataSnapshot | null> {
    try {
      const wsRequest = new WebSocketRequest(
        '/export/refreshCustomerListFromExportServices',
        '',
        true,
      );

      const result = await sendRequestAndListenForResponse(
        wsRequest,
        'refreshCustomerListFromExportServices',
      );

      return result as CustomerDataSnapshot;
    } catch (error) {
      console.error(
        'Error refreshing customer list from export services:',
        error,
      );
      return null;
    }
  }

  return {
    integrations,
    isLoading,
    fetchIntegrations,
    saveIntegration,
    updateExportUserToken,
    exchangeCodeForTokens,
    pkceCode,
    pkceChallenge,
    generatePkce,
    exportTypes,
    fetchExportTypes,
    getCustomerPullExportUsers,
    hasCustomerPullCapability,
    getPrimaryCustomerPullExportUser,
    getCustomerListFromExportServices,
    refreshCustomerListFromExportServices,
  };
});
